#!/usr/bin/env python3
"""
坐标标记处理器 - 简化版本

参考前端项目的方式，将坐标归一化和图片标记合并处理
"""

import os
import re
import base64
from typing import Tuple, Optional
from PIL import Image, ImageDraw
from loguru import logger

from src.domain.ui_task.mobile.android.screenshot_manager import screenshot_manager


class CoordinateMarker:
    """坐标标记处理器"""

    def __init__(self):
        # 标记样式配置
        self.marker_color = "red"
        self.marker_size = 10  # 标记点的半径

    def parse_and_mark_coordinates(self, image_path: str, response: str, task_id: str) -> <PERSON><PERSON>[Optional[Tuple[int, int]], str]:
        """
        解析坐标并在图片上标记，参考前端项目的方式
        
        Args:
            image_path: 原始截图路径（相对路径）
            response: 包含坐标的响应字符串
            task_id: 任务ID
            
        Returns:
            Tuple[坐标元组, 标记后的图片路径]
        """
        try:
            # 获取完整路径
            if not os.path.isabs(image_path):
                full_image_path = screenshot_manager.get_screenshot_full_path(image_path)
            else:
                full_image_path = image_path

            if not os.path.exists(full_image_path):
                logger.error(f"❌ Screenshot file not found: {full_image_path}")
                return None, image_path

            # 打开图片并获取原始尺寸
            image = Image.open(full_image_path)
            original_width, original_height = image.size
            logger.info(f"📸 Image size: {original_width}x{original_height}")

            # 解析坐标（参考你提供的方式）
            match = re.search(r'<point>(\d+)\s+(\d+)</point>', response)
            if match:
                # 提取坐标并进行归一化处理
                raw_x = int(match.group(1))
                raw_y = int(match.group(2))
                
                # 归一化：除以1000然后乘以图片实际尺寸
                x = int(raw_x / 1000 * original_width)
                y = int(raw_y / 1000 * original_height)
                
                action_point = (x, y)
                logger.info(f"🎯 坐标解析和归一化: ({raw_x}, {raw_y}) -> {action_point}")
                
                # 在原图上标记动作位置
                draw = ImageDraw.Draw(image)
                self._draw_marker(draw, action_point)
                
                # 保存标记后的图片
                marked_image_path = self._save_marked_image(image, image_path, task_id)
                
                return action_point, marked_image_path
            else:
                logger.warning(f"⚠️ Failed to parse coordinates from response: {response}")
                return None, image_path

        except Exception as e:
            logger.error(f"❌ Error in parse_and_mark_coordinates: {str(e)}")
            return None, image_path

    def _draw_marker(self, draw: ImageDraw.Draw, point: Tuple[int, int]) -> None:
        """
        在指定位置绘制标记点
        
        Args:
            draw: PIL绘图对象
            point: 标记点坐标 (x, y)
        """
        x, y = point
        
        # 绘制圆形标记点
        draw.ellipse(
            (x - self.marker_size, y - self.marker_size, 
             x + self.marker_size, y + self.marker_size), 
            fill=self.marker_color
        )
        
        logger.debug(f"📍 Marker drawn at {point}")

    def _save_marked_image(self, image: Image.Image, original_path: str, task_id: str) -> str:
        """
        保存标记后的图片
        
        Args:
            image: 标记后的PIL图片对象
            original_path: 原始图片路径
            task_id: 任务ID
            
        Returns:
            标记后图片的相对路径
        """
        try:
            # 生成标记后的文件名
            if original_path.endswith('.png'):
                marked_path = original_path.replace('.png', '_marked.png')
            else:
                marked_path = f"{original_path}_marked.png"
            
            # 获取完整路径
            if not os.path.isabs(marked_path):
                full_marked_path = screenshot_manager.get_screenshot_full_path(marked_path)
            else:
                full_marked_path = marked_path
            
            # 保存图片
            image.save(full_marked_path)
            logger.info(f"📸 Marked image saved: {marked_path}")
            
            return marked_path
            
        except Exception as e:
            logger.error(f"❌ Failed to save marked image: {str(e)}")
            return original_path

    def parse_coordinates_only(self, response: str, image_width: int, image_height: int) -> Optional[Tuple[int, int]]:
        """
        仅解析坐标，不进行图片标记
        
        Args:
            response: 包含坐标的响应字符串
            image_width: 图片宽度
            image_height: 图片高度
            
        Returns:
            归一化后的坐标元组或None
        """
        try:
            match = re.search(r'<point>(\d+)\s+(\d+)</point>', response)
            if match:
                raw_x = int(match.group(1))
                raw_y = int(match.group(2))
                
                # 归一化处理
                x = int(raw_x / 1000 * image_width)
                y = int(raw_y / 1000 * image_height)
                
                logger.debug(f"🎯 坐标解析: ({raw_x}, {raw_y}) -> ({x}, {y})")
                return (x, y)
            else:
                logger.warning(f"⚠️ No coordinates found in response: {response}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error parsing coordinates: {str(e)}")
            return None

    def parse_drag_coordinates(self, response: str, image_width: int, image_height: int) -> Optional[Tuple[Tuple[int, int], Tuple[int, int]]]:
        """
        解析拖拽动作的起始和结束坐标
        
        Args:
            response: 包含坐标的响应字符串
            image_width: 图片宽度
            image_height: 图片高度
            
        Returns:
            ((start_x, start_y), (end_x, end_y)) 或 None
        """
        try:
            # 查找所有坐标点
            matches = re.findall(r'<point>(\d+)\s+(\d+)</point>', response)
            if len(matches) >= 2:
                # 起始坐标
                start_raw_x, start_raw_y = int(matches[0][0]), int(matches[0][1])
                start_x = int(start_raw_x / 1000 * image_width)
                start_y = int(start_raw_y / 1000 * image_height)
                
                # 结束坐标
                end_raw_x, end_raw_y = int(matches[1][0]), int(matches[1][1])
                end_x = int(end_raw_x / 1000 * image_width)
                end_y = int(end_raw_y / 1000 * image_height)
                
                logger.debug(f"🎯 拖拽坐标解析: ({start_raw_x}, {start_raw_y}) -> ({start_x}, {start_y}), "
                           f"({end_raw_x}, {end_raw_y}) -> ({end_x}, {end_y})")
                
                return ((start_x, start_y), (end_x, end_y))
            else:
                logger.warning(f"⚠️ Insufficient coordinates for drag action: {response}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error parsing drag coordinates: {str(e)}")
            return None

    @staticmethod
    def convert_image_path_to_base64(image_path: str) -> Optional[str]:
        """
        将图片路径转换为base64编码

        Args:
            image_path: 图片文件路径

        Returns:
            base64编码的图片数据或None
        """
        try:
            # 获取完整路径
            if not os.path.isabs(image_path):
                full_path = screenshot_manager.get_screenshot_full_path(image_path)
            else:
                full_path = image_path

            if not os.path.exists(full_path):
                logger.error(f"❌ Image file not found: {full_path}")
                return None

            with open(full_path, "rb") as f:
                image_content = f.read()

            image_data_base64 = base64.b64encode(image_content).decode("utf-8")
            logger.debug(f"📸 Image converted to base64: {image_path}")
            return image_data_base64

        except Exception as e:
            logger.error(f"❌ Failed to convert image to base64: {str(e)}")
            return None


# 全局实例
coordinate_marker = CoordinateMarker()


# 兼容性接口
def parse_and_mark_coordinates(image_path: str, response: str, task_id: str) -> Tuple[Optional[Tuple[int, int]], str]:
    """兼容性接口：解析坐标并标记图片"""
    return coordinate_marker.parse_and_mark_coordinates(image_path, response, task_id)


def parse_coordinates_from_response(response: str, image_width: int, image_height: int) -> Optional[Tuple[int, int]]:
    """兼容性接口：仅解析坐标"""
    return coordinate_marker.parse_coordinates_only(response, image_width, image_height)
