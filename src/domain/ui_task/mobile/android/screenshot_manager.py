#!/usr/bin/env python3
"""
截图管理工具

负责任务截图的文件系统管理和设备截图操作：
1. 为每个任务创建独立的截图目录
2. 管理截图文件的保存和访问
3. 任务删除时清理截图目录
4. 提供ADB设备操作和截图功能
"""
import base64
import os
import shutil
import subprocess
from datetime import datetime
from pathlib import Path

from loguru import logger
from config.globalconfig import get_or_create_settings_ins


class ScreenshotManager:
    """截图管理器"""

    def __init__(self, base_dir: str = None):
        """
        初始化截图管理器

        Args:
            base_dir: 截图根目录，如果为None则从配置文件读取
        """
        if base_dir is None:
            # 从配置文件读取路径
            config = get_or_create_settings_ins()
            base_dir = config.paths.screenshot_base_dir

        self.base_dir = Path(base_dir)
        # 使用 parents=True 确保父目录被创建
        self.base_dir.mkdir(parents=True, exist_ok=True)
    
    def get_task_screenshot_dir(self, task_id: str) -> Path:
        """
        获取任务的截图目录路径
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务截图目录路径
        """
        task_dir = self.base_dir / task_id
        task_dir.mkdir(exist_ok=True)
        return task_dir

    def get_screenshot_full_path(self, relative_path: str) -> Path:
        """
        根据相对路径获取截图的完整路径
        
        Args:
            relative_path: 截图的相对路径
            
        Returns:
            截图的完整路径
        """
        return self.base_dir / relative_path

    def screenshot_exists(self, relative_path: str) -> bool:
        """
        检查截图文件是否存在
        
        Args:
            relative_path: 截图的相对路径
            
        Returns:
            文件是否存在
        """
        full_path = self.get_screenshot_full_path(relative_path)
        return full_path.exists()

    def delete_task_screenshots(self, task_id: str) -> bool:
        """
        删除任务的所有截图
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否删除成功
        """
        try:
            task_dir = self.base_dir / task_id

            if task_dir.exists():
                shutil.rmtree(task_dir)
                logger.info(f"[task_id: {task_id}] 🗑️ Task screenshots deleted: {task_dir}")
                return True
            else:
                logger.info(f"[task_id: {task_id}] ⚠️ Task screenshot directory not found: {task_dir}")
                return True  # 目录不存在也算成功

        except Exception as e:
            logger.error(f"[task_id: {task_id}] ❌ Failed to delete task screenshots: {str(e)}")
            return False

    def execute_adb(self, adb_command: str) -> str:
        """
        执行ADB命令

        Args:
            adb_command: ADB命令字符串

        Returns:
            命令执行结果或ERROR
        """
        try:
            # 执行ADB命令
            result = subprocess.run(
                adb_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30  # 添加超时
            )
            if result.returncode == 0:
                return result.stdout.strip()
            logger.error(f"Command execution failed: {adb_command}")
            logger.error(result.stderr)
            return "ERROR"
        except subprocess.TimeoutExpired:
            logger.error(f"Command timeout: {adb_command}")
            return "ERROR"
        except Exception as e:
            logger.error(f"Command exception: {adb_command}, error: {str(e)}")
            return "ERROR"

    def take_screenshot(self,
                        device: str = "emulator",
                        task_id: str = None,
                        execution_count: int = 0,
                        action_name: str = None
                        ) -> str:
        """
        截取设备屏幕截图并保存到任务目录

        Args:
            device: 设备ID
            task_id: 任务ID
            execution_count: 执行轮数
            action_name: 动作名称

        Returns:
            截图文件的相对路径或错误信息
        """
        try:
            # 获取任务截图目录
            task_dir = self.get_task_screenshot_dir(task_id)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            if action_name:
                filename = f"{action_name}_{timestamp}.png"
            else:
                filename = f"screenshot_{execution_count}_{timestamp}.png"

            screenshot_file = task_dir / filename

            # 使用简化的截图命令
            adb_command = f"adb -s {device} exec-out screencap -p > {screenshot_file}"

            # 执行截图命令
            self.execute_adb(adb_command)

            # 检查文件是否成功创建
            if screenshot_file.exists() and screenshot_file.stat().st_size > 0:
                # 返回相对路径
                relative_path = str(screenshot_file.relative_to(self.base_dir))
                logger.info(f"[task_id: {task_id}] 📸 Screenshot saved: {relative_path}")
                return relative_path
            else:
                # 尝试备用方法
                return self._take_screenshot_fallback(device, str(screenshot_file), task_id)

        except Exception as e:
            logger.error(f"Screenshot failed, error: {str(e)}")
            return f"Screenshot failed, error information: {str(e)}"

    def _take_screenshot_fallback(self, device: str, screenshot_file: str, task_id: str = None) -> str:
        """
        备用截图方法

        Args:
            device: 设备ID
            screenshot_file: 截图文件路径
            task_id: 任务ID

        Returns:
            截图文件路径或错误信息
        """
        try:
            # 备用方法：先保存到设备，再拉取
            remote_file = f"/sdcard/temp_screenshot_{datetime.now().strftime('%H%M%S')}.png"

            # 截图到设备
            cap_command = f"adb -s {device} shell screencap -p {remote_file}"
            if self.execute_adb(cap_command) != "ERROR":
                # 拉取到本地
                pull_command = f"adb -s {device} pull {remote_file} {screenshot_file}"
                if self.execute_adb(pull_command) != "ERROR":
                    # 删除设备上的临时文件
                    delete_command = f"adb -s {device} shell rm {remote_file}"
                    self.execute_adb(delete_command)

                    if os.path.exists(screenshot_file) and os.path.getsize(screenshot_file) > 0:
                        # 返回相对路径
                        if task_id:
                            screenshot_path = Path(screenshot_file)
                            relative_path = str(screenshot_path.relative_to(self.base_dir))
                            return relative_path
                        return screenshot_file

            return "Screenshot failed. Please check device connection or permissions."

        except Exception as e:
            return f"Fallback screenshot failed: {str(e)}"


# 全局截图管理器实例
screenshot_manager = ScreenshotManager()


# 向后兼容的函数，委托给screenshot_manager实例
def execute_adb(adb_command: str) -> str:
    """
    执行ADB命令

    Args:
        adb_command: ADB命令字符串

    Returns:
        命令执行结果或ERROR
    """
    return screenshot_manager.execute_adb(adb_command)


def take_screenshot(
        device: str = "emulator",
        task_id: str = None,
        execution_count: int = 0,
        action_name: str = None
) -> str:
    """
    截取设备屏幕截图并保存到任务目录

    Args:
        device: 设备ID
        task_id: 任务ID
        execution_count: 执行轮数
        action_name: 动作名称

    Returns:
        截图文件的相对路径或错误信息
    """
    return screenshot_manager.take_screenshot(device, task_id, execution_count, action_name)


def convert_screenshot_to_base64(screenshot_path: str, task_id: str) -> str:
    """
    将截图文件转换为base64格式

    Args:
        screenshot_path: 截图文件路径（可以是相对路径或绝对路径）
        task_id: 任务ID

    Returns:
        base64编码的图片数据，失败返回空字符串
    """
    try:
        # 如果是相对路径，转换为绝对路径
        if not os.path.isabs(screenshot_path):
            full_path = screenshot_manager.get_screenshot_full_path(screenshot_path)
        else:
            full_path = screenshot_path

        with open(full_path, "rb") as f:
            image_content = f.read()
        image_data_base64 = base64.b64encode(image_content).decode("utf-8")
        return image_data_base64
    except Exception as e:
        logger.info(f"[{task_id}] ❌ Failed to convert screenshot to base64: {str(e)}")
        return ""