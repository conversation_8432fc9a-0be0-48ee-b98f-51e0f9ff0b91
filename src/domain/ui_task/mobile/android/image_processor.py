#!/usr/bin/env python3
"""
统一的图像处理模块 - 合并图像标注和图像处理工具功能

本模块整合了原有的两个图像工具模块：
- src.domain.ui_task.mobile.android.image_tools.image_annotator
- src.domain.ui_task.mobile.android.image_tools.image_utils

主要功能：
1. 图像标注：在截图上标注操作坐标和动作位置
2. 图像处理：base64转换、格式转换、大小调整、压缩、裁剪等

向后兼容性：
- image_processor: 新的统一实例（推荐使用）
- image_annotator: 向后兼容的别名，指向同一个实例

使用示例：
    # 新接口
    from src.domain.ui_task.mobile.android.image_processor import image_processor
    result = image_processor.annotate_screenshot_from_action(path, action, task_id)
    
    # 旧接口（向后兼容）
    from src.domain.ui_task.mobile.android.image_processor import image_annotator
    result = image_annotator.annotate_screenshot_from_action(path, action, task_id)
"""

import base64
import os
from pathlib import Path
from typing import Tuple, Optional

from PIL import Image, ImageDraw
from loguru import logger

from src.domain.ui_task.mobile.android.action_tool import parse_action_with_coordinates, ScreenUtils
from src.domain.ui_task.mobile.android.screenshot_manager import screenshot_manager


class ImageProcessor:
    """统一的图像处理器类，整合图像标注和图像处理工具功能"""

    def __init__(self):
        # 图像标注配置 - 更显眼的标记设计
        self.primary_color = (255, 0, 0)
        self.accent_color = (255, 0, 0)  #
        self.base_size = 40  # 基础标记大小
        self.line_width = 4  # 线条宽度

    def _draw_enhanced_marker(self, draw, x: int, y: int, img_width: int, img_height: int):
        """
        绘制增强的多层标记，让模型更容易识别已操作的位置

        标记设计：
        1. 外圈大圆环（亮绿色）
        2. 中圈圆环（红色）
        3. 内圈小圆环（黄色）
        4. 中心十字准星（白色）
        5. 中心实心圆点（黑色）

        Args:
            draw: PIL绘图对象
            x, y: 标记中心坐标
            img_width, img_height: 图片尺寸
        """
        # 根据图片尺寸动态调整标记大小
        scale_factor = min(img_width, img_height) / 1000.0
        scale_factor = max(0.5, min(2.0, scale_factor))  # 限制缩放范围

        # 计算各层标记的半径
        outer_radius = int(self.base_size * scale_factor * 0.8)  # 外圈大圆环
        middle_radius = int(self.base_size * scale_factor * 0.6)  # 中圈圆环
        inner_radius = int(self.base_size * scale_factor * 0.4)  # 内圈小圆环
        cross_length = int(self.base_size * scale_factor * 0.5)  # 十字准星长度
        center_radius = int(self.base_size * scale_factor * 0.1)  # 中心圆点

        # 确保标记不超出图片边界
        max_radius = min(
            x, y,
            img_width - x - 1,
            img_height - y - 1,
            outer_radius
        )

        if max_radius < 10:  # 如果空间太小，使用简化标记
            self._draw_simple_marker(draw, x, y)
            return

        # 按比例调整所有半径
        if max_radius < outer_radius:
            ratio = max_radius / outer_radius
            outer_radius = int(outer_radius * ratio)
            middle_radius = int(middle_radius * ratio)
            inner_radius = int(inner_radius * ratio)
            cross_length = int(cross_length * ratio)
            center_radius = max(1, int(center_radius * ratio))

        # 2. 绘制中圈圆环（红色，中等线条）
        draw.ellipse(
            [x - middle_radius, y - middle_radius, x + middle_radius, y + middle_radius],
            outline=self.primary_color,
            width=self.line_width - 2
        )

        # # 3. 绘制内圈小圆环（黄色，细线条）
        # draw.ellipse(
        #     [x - inner_radius, y - inner_radius, x + inner_radius, y + inner_radius],
        #     outline=self.accent_color,
        #     width=max(2, self.line_width - 4)
        # )
        #
        # # 4. 绘制十字准星（白色，突出显示精确位置）
        # cross_width = max(2, self.line_width - 3)
        # # 水平线
        # draw.line(
        #     [x - cross_length, y, x + cross_length, y],
        #     fill=self.primary_color,
        #     width=cross_width
        # )
        # # 垂直线
        # draw.line(
        #     [x, y - cross_length, x, y + cross_length],
        #     fill=self.primary_color,
        #     width=cross_width
        # )

        # 5. 绘制中心实心圆点（黑色，标记精确点击位置）
        draw.ellipse(
            [x - center_radius, y - center_radius, x + center_radius, y + center_radius],
            fill=self.primary_color
        )

        logger.info(f"📍 Enhanced marker drawn at ({x}, {y}) with outer_radius={outer_radius}")

    def _draw_simple_marker(self, draw, x: int, y: int):
        """
        当空间不足时绘制简化标记

        Args:
            draw: PIL绘图对象
            x, y: 标记中心坐标
        """
        # 简化的十字准星
        cross_length = 8
        cross_width = 3

        # 绘制十字准星（亮绿色）
        draw.line(
            [x - cross_length, y, x + cross_length, y],
            fill=self.primary_color,
            width=cross_width
        )
        draw.line(
            [x, y - cross_length, x, y + cross_length],
            fill=self.primary_color,
            width=cross_width
        )

        # 中心圆点（红色）
        draw.ellipse(
            [x - 2, y - 2, x + 2, y + 2],
            fill=self.primary_color
        )

        logger.info(f"📍 Simple marker drawn at ({x}, {y})")

    @staticmethod
    def parse_coordinates_from_action(action_command: str, device: str = None) -> Optional[Tuple[int, int]]:
        """
        从动作命令中解析坐标，复用Android工具中的解析逻辑

        Args:
            action_command: 动作命令，如 "click(start_box='(1060,2567)')"
            device: 设备ID，用于坐标归一化

        Returns:
            坐标元组 (x, y) 或 None
        """
        try:
            # 如果没有传递设备参数，使用系统默认设备
            if device is None:
                from src.schema.constants import SystemConstants
                device = SystemConstants.DEFAULT_DEVICE

            # 使用现有的Android工具解析动作，传递设备参数进行坐标归一化
            parsed_action = parse_action_with_coordinates(action_command, device)

            if parsed_action is None:
                logger.info(f"📷 No coordinates found in action '{action_command}'")
                return None

            # 提取坐标信息
            action_type = parsed_action.get("action", "")

            # 处理点击和长按动作
            if action_type in ["click", "long_press"]:
                x = parsed_action.get("x")
                y = parsed_action.get("y")
                if x is not None and y is not None:
                    logger.info(f"📍 Parsed coordinates from '{action_command}': ({x}, {y})")
                    return (x, y)

            # 处理拖拽动作（使用起始坐标）
            elif action_type == "drag":
                start_x = parsed_action.get("start_x")
                start_y = parsed_action.get("start_y")
                if start_x is not None and start_y is not None:
                    logger.info(f"📍 Parsed drag start coordinates from '{action_command}': ({start_x}, {start_y})")
                    return (start_x, start_y)

            # 处理滚动动作
            elif action_type == "scroll":
                start_x = parsed_action.get("start_x")
                start_y = parsed_action.get("start_y")
                if start_x is not None and start_y is not None:
                    logger.info(f"📍 Parsed scroll coordinates from '{action_command}': ({start_x}, {start_y})")
                    return (start_x, start_y)

            logger.info(f"📷 No coordinates available for action type '{action_type}' in '{action_command}'")
            return None

        except Exception as e:
            logger.error(f"❌ Error parsing coordinates from '{action_command}': {str(e)}")
            return None

    def annotate_screenshot_with_coordinates(self, image_path: str, coordinates: Tuple[int, int],
                                             task_id: str) -> str:
        """
        在截图上标注操作坐标
        
        Args:
            image_path: 原始截图路径（相对路径）
            coordinates: 操作坐标 (x, y)
            task_id: 任务ID
            
        Returns:
            标注后的截图路径（相对路径）
        """
        try:
            # 获取完整路径
            full_image_path = screenshot_manager.get_screenshot_full_path(image_path)

            if not os.path.exists(full_image_path):
                logger.error(f"❌ Screenshot file not found: {full_image_path}")
                return image_path

            # 打开图片
            with Image.open(full_image_path) as img:
                # 创建绘图对象
                draw = ImageDraw.Draw(img)

                # 获取图片尺寸
                img_width, img_height = img.size
                x, y = coordinates

                # 确保坐标在图片范围内
                if x < 0 or y < 0 or x > img_width or y > img_height:
                    logger.warning(f"⚠️ Coordinates ({x}, {y}) are outside image bounds ({img_width}x{img_height})")
                    # 调整坐标到图片范围内
                    x = max(0, min(x, img_width - 1))
                    y = max(0, min(y, img_height - 1))

                # 绘制多层显眼标记
                self._draw_enhanced_marker(draw, x, y, img_width, img_height)

                # 直接覆盖原始图片文件，不生成新文件
                img.save(full_image_path, 'PNG')

                logger.info(f"📸 Screenshot annotated in place: {image_path}")

                # 返回原始相对路径
                return image_path

        except Exception as e:
            logger.error(f"❌ Error annotating screenshot: {str(e)}")
            return image_path

    def annotate_screenshot_from_action(self, image_path: str, action_command: str,
                                        task_id: str, device: str = None) -> str:
        """
        根据动作命令在截图上标注操作位置

        Args:
            image_path: 原始截图路径（相对路径）
            action_command: 动作命令
            task_id: 任务ID
            device: device

        Returns:
            标注后的截图路径（相对路径），如果无法解析坐标则返回原路径
        """
        try:
            # 如果没有传递设备参数或设备参数为空，使用系统默认设备
            if not device:
                from src.schema.constants import SystemConstants
                device = SystemConstants.DEFAULT_DEVICE

            # 解析坐标，传递设备参数进行坐标归一化
            coordinates = self.parse_coordinates_from_action(action_command, device)

            if coordinates is None:
                logger.info(f"📷 No coordinates found in action '{action_command}', returning original screenshot")
                return image_path

            x, y = coordinates
            logger.info(f"📍 Parsed coordinates from '{action_command}': ({x}, {y})")
            if "scroll" in action_command or "drag" in action_command:
                screen_width, screen_height = ScreenUtils.get_screen_size(device)
                if x >= screen_width - 10:
                    x = screen_width - 50

                if x <= 10:
                    x = 50

                if y >= screen_height - 10:
                    y = screen_height - 50

                if y <= 10:
                    y = 50

                coordinates = (x, y)
            # 标注截图
            return self.annotate_screenshot_with_coordinates(image_path, coordinates, task_id)

        except Exception as e:
            logger.error(f"❌ Error in annotate_screenshot_from_action: {str(e)}")
            return image_path

    @staticmethod
    def convert_image_path_to_base64(image_path: Optional[str]) -> Optional[str]:
        """
        将图片文件路径转换为base64格式

        Args:
            image_path: 图片文件的完整路径，如果为None或空字符串则返回None

        Returns:
            base64编码的图片数据，失败或路径为空时返回None
        """
        if not image_path or not image_path.strip():
            return None

        try:
            image_path = image_path.strip()
            full_image_path = Path(image_path)

            # 检查文件是否存在
            if not full_image_path.exists():
                logger.error(f"❌ Image file not found: {full_image_path}")
                return None

            # 检查是否为文件
            if not full_image_path.is_file():
                logger.error(f"❌ Path is not a file: {full_image_path}")
                return None

            # 读取文件并转换为base64
            with open(full_image_path, "rb") as f:
                image_content = f.read()

            image_data_base64 = base64.b64encode(image_content).decode("utf-8")
            logger.info(f"✅ Successfully converted image to base64: {image_path} ({len(image_data_base64)} chars)")
            return image_data_base64

        except Exception as e:
            logger.error(f"❌ Failed to convert image to base64: {image_path}, error: {str(e)}")
            return None


# 创建统一的图像处理器实例
image_processor = ImageProcessor()

# 向后兼容的别名
image_annotator = image_processor
