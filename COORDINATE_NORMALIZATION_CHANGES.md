# 坐标归一化功能实现

## 概述

根据用户需求，修改了模型输出坐标的处理方式，实现了坐标归一化功能：
- 模型输出的坐标除以1000
- 然后乘以界面的实际宽高
- 确保坐标在屏幕范围内

## 修改的文件

### 1. `src/domain/ui_task/mobile/android/action_tool.py`

#### 主要修改：

1. **ActionParser 类构造函数**
   - 添加了 `device` 参数，用于获取屏幕尺寸
   - 初始化时保存设备信息

2. **新增 `_normalize_coordinates` 方法**
   ```python
   def _normalize_coordinates(self, x: int, y: int) -> Tuple[int, int]:
       """
       归一化坐标处理
       模型输出的坐标需要除以1000然后乘以界面宽高
       """
       # 获取屏幕尺寸
       screen_width, screen_height = ScreenUtils.get_screen_size(self.device)
       
       # 归一化处理：坐标除以1000然后乘以界面宽高
       normalized_x = int((x / 1000.0) * screen_width)
       normalized_y = int((y / 1000.0) * screen_height)
       
       # 确保坐标在屏幕范围内
       normalized_x = max(0, min(normalized_x, screen_width))
       normalized_y = max(0, min(normalized_y, screen_height))
       
       return normalized_x, normalized_y
   ```

3. **修改 `_extract_coordinates` 方法**
   - 在提取坐标后自动调用归一化处理
   - 支持新格式 `<point>x y</point>` 和旧格式 `(x,y)`

4. **修改 `_parse_drag` 方法**
   - 对拖拽动作的起始和结束坐标都进行归一化处理

5. **更新 ActionExecutor 类**
   - 构造函数接受设备参数
   - 动态更新解析器以适应不同设备

6. **更新兼容性接口**
   - `parse_action_with_coordinates` 函数添加设备参数
   - 保持向后兼容性

### 2. `src/domain/ui_task/mobile/android/image_processor.py`

#### 修改：
- `parse_coordinates_from_action` 方法添加设备参数
- 调用 `parse_action_with_coordinates` 时传递设备信息

### 3. `src/domain/ui_task/mobile/aggregate/agent/execution_agent.py`

#### 修改：
- 更新所有调用 `parse_action_with_coordinates` 的地方
- 传递设备参数以启用坐标归一化

## 功能特性

### 1. 坐标归一化算法
```
normalized_x = (raw_x / 1000.0) * screen_width
normalized_y = (raw_y / 1000.0) * screen_height
```

### 2. 边界检查
- 确保归一化后的坐标不超出屏幕范围
- 负坐标被限制为0
- 超出屏幕的坐标被限制为屏幕边界

### 3. 支持的动作类型
- **点击动作**: `click <point>x y</point>`
- **长按动作**: `long_press <point>x y</point>`
- **拖拽动作**: `drag <point>x1 y1</point> <point>x2 y2</point>`
- **滚动动作**: `scroll <point>x y</point> direction`

### 4. 多屏幕尺寸支持
- 自动获取设备屏幕尺寸
- 根据实际屏幕尺寸进行归一化
- 支持不同分辨率的设备

## 测试验证

创建了测试文件验证功能：
- `simple_coordinate_test.py`: 简化的坐标归一化测试
- 测试了各种坐标格式和边界情况
- 验证了不同屏幕尺寸的归一化效果

### 测试结果示例
```
原始坐标: (500, 500) -> 归一化后: (540, 960) (1080x1920屏幕)
原始坐标: (100, 100) -> 归一化后: (108, 192) (1080x1920屏幕)
原始坐标: (900, 900) -> 归一化后: (972, 1728) (1080x1920屏幕)
```

## 向后兼容性

- 保持了所有现有接口的兼容性
- 添加了可选的设备参数
- 默认使用系统默认设备
- 不影响现有代码的正常运行

## 使用方式

### 新的调用方式（推荐）
```python
# 传递设备参数进行坐标归一化
parsed_action = parse_action_with_coordinates(action_str, device_id)
```

### 兼容的调用方式
```python
# 使用默认设备
parsed_action = parse_action_with_coordinates(action_str)
```

## 注意事项

1. **设备参数**: 建议在调用时传递正确的设备ID，以确保获取准确的屏幕尺寸
2. **坐标范围**: 模型输出的坐标应该在0-1000范围内，超出范围的坐标会被自动限制
3. **屏幕尺寸**: 系统会自动通过ADB获取屏幕尺寸，如果获取失败会使用默认值(1080x1920)
4. **日志记录**: 归一化过程会记录详细的调试日志，便于问题排查

## 总结

成功实现了坐标归一化功能，满足了用户的需求：
- ✅ 坐标除以1000然后乘以界面宽高
- ✅ 调用Android工具类获取屏幕尺寸
- ✅ 支持所有坐标相关的动作类型
- ✅ 保持向后兼容性
- ✅ 通过了完整的测试验证
