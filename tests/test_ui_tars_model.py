#!/usr/bin/env python3
"""
UI-TARS模型测试脚本

测试ByteDance-Seed/UI-TARS-1.5-7B模型的各种功能：
1. 基本连接测试
2. 文本生成测试
3. UI相关任务测试
4. 性能测试
5. 错误处理测试
"""

import json
import time
import sys
import os
from typing import Dict, Any, List
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from openai import OpenAI
from langchain_core.prompts import ChatPromptTemplate
from loguru import logger

from config.globalconfig import get_or_create_settings_ins
from src.infra.model import get_chat_model, get_coordinate_model, get_coordinate_model_name


class UITarsModelTester:
    """UI-TARS模型测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.config = get_or_create_settings_ins()
        self.test_results = []
        self.start_time = None
        
        # 初始化OpenAI客户端（直接方式）
        ui_tars_config = self.config.llms.get("ui_tars")
        if not ui_tars_config:
            raise ValueError("未找到ui_tars模型配置")
            
        self.openai_client = OpenAI(
            base_url=ui_tars_config.external_args.get("base_url"),
            api_key=ui_tars_config.external_args.get("api_key")
        )
        self.model_name = ui_tars_config.model
        self.temperature = ui_tars_config.temperature
        
        # 初始化LangChain方式（如果ui_tars被设置为coordinate模型）
        try:
            self.coordinate_client = get_coordinate_model()
            self.coordinate_model_name = get_coordinate_model_name()
        except Exception as e:
            logger.warning(f"无法初始化coordinate模型: {e}")
            self.coordinate_client = None
            self.coordinate_model_name = None
    
    def log_test_result(self, test_name: str, success: bool, details: Dict[str, Any]):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} {test_name}: {details.get('message', '')}")
    
    def test_basic_connection(self) -> bool:
        """测试基本连接"""
        logger.info("🔗 测试基本连接...")
        
        try:
            start_time = time.time()
            
            response = self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "user", "content": "Hello, please respond with 'Connection successful'"}
                ],
                temperature=0,
                max_tokens=50
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            content = response.choices[0].message.content
            
            success = "successful" in content.lower() or "hello" in content.lower()
            
            self.log_test_result(
                "basic_connection",
                success,
                {
                    "message": f"响应时间: {response_time:.2f}s",
                    "response": content,
                    "response_time": response_time
                }
            )
            
            return success
            
        except Exception as e:
            self.log_test_result(
                "basic_connection",
                False,
                {
                    "message": f"连接失败: {str(e)}",
                    "error": str(e)
                }
            )
            return False
    
    def test_text_generation(self) -> bool:
        """测试文本生成能力"""
        logger.info("📝 测试文本生成能力...")
        
        test_prompts = [
            "请简单介绍一下你自己",
            "解释什么是UI自动化测试",
            "描述移动应用界面的基本组成元素"
        ]
        
        all_success = True
        
        for i, prompt in enumerate(test_prompts):
            try:
                start_time = time.time()
                
                response = self.openai_client.chat.completions.create(
                    model=self.model_name,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=self.temperature,
                    max_tokens=200
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                content = response.choices[0].message.content
                
                # 简单的质量检查
                success = len(content) > 10 and not content.startswith("Error")
                
                self.log_test_result(
                    f"text_generation_{i+1}",
                    success,
                    {
                        "message": f"提示词 {i+1} - 响应时间: {response_time:.2f}s, 长度: {len(content)}",
                        "prompt": prompt,
                        "response": content[:100] + "..." if len(content) > 100 else content,
                        "response_time": response_time,
                        "response_length": len(content)
                    }
                )
                
                if not success:
                    all_success = False
                    
                # 避免请求过于频繁
                time.sleep(1)
                
            except Exception as e:
                self.log_test_result(
                    f"text_generation_{i+1}",
                    False,
                    {
                        "message": f"文本生成失败: {str(e)}",
                        "prompt": prompt,
                        "error": str(e)
                    }
                )
                all_success = False
        
        return all_success
    
    def test_ui_understanding(self) -> bool:
        """测试UI理解能力"""
        logger.info("🖥️ 测试UI理解能力...")
        
        ui_prompts = [
            {
                "prompt": "在一个移动应用界面中，如果我想点击'登录'按钮，我应该寻找什么样的UI元素？",
                "expected_keywords": ["按钮", "button", "登录", "login"]
            },
            {
                "prompt": "描述一个典型的移动应用导航栏包含哪些元素",
                "expected_keywords": ["导航", "菜单", "图标", "标题"]
            },
            {
                "prompt": "如何识别一个输入框元素？它通常有什么特征？",
                "expected_keywords": ["输入", "文本", "框", "field", "input"]
            }
        ]
        
        all_success = True
        
        for i, test_case in enumerate(ui_prompts):
            try:
                start_time = time.time()
                
                response = self.openai_client.chat.completions.create(
                    model=self.model_name,
                    messages=[{"role": "user", "content": test_case["prompt"]}],
                    temperature=self.temperature,
                    max_tokens=300
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                content = response.choices[0].message.content.lower()
                
                # 检查是否包含期望的关键词
                keyword_matches = sum(1 for keyword in test_case["expected_keywords"] 
                                    if keyword.lower() in content)
                success = keyword_matches >= len(test_case["expected_keywords"]) // 2
                
                self.log_test_result(
                    f"ui_understanding_{i+1}",
                    success,
                    {
                        "message": f"UI理解测试 {i+1} - 关键词匹配: {keyword_matches}/{len(test_case['expected_keywords'])}",
                        "prompt": test_case["prompt"],
                        "response": response.choices[0].message.content[:150] + "...",
                        "keyword_matches": keyword_matches,
                        "expected_keywords": test_case["expected_keywords"],
                        "response_time": response_time
                    }
                )
                
                if not success:
                    all_success = False
                    
                time.sleep(1)
                
            except Exception as e:
                self.log_test_result(
                    f"ui_understanding_{i+1}",
                    False,
                    {
                        "message": f"UI理解测试失败: {str(e)}",
                        "prompt": test_case["prompt"],
                        "error": str(e)
                    }
                )
                all_success = False
        
        return all_success
    
    def test_coordinate_model_integration(self) -> bool:
        """测试作为坐标模型的集成"""
        logger.info("🎯 测试坐标模型集成...")
        
        if not self.coordinate_client:
            self.log_test_result(
                "coordinate_integration",
                False,
                {
                    "message": "坐标模型客户端未初始化",
                    "error": "coordinate_client is None"
                }
            )
            return False
        
        try:
            # 模拟坐标获取请求
            coordinate_prompt = """
            在一个移动应用界面中，我需要点击"确认"按钮。
            请分析界面并返回点击坐标。
            
            界面描述：屏幕尺寸为1080x1920，"确认"按钮位于屏幕底部中央区域。
            """
            
            start_time = time.time()
            
            response = self.coordinate_client.chat.completions.create(
                model=self.coordinate_model_name,
                messages=[{"role": "user", "content": coordinate_prompt}],
                temperature=0,
                max_tokens=200
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            content = response.choices[0].message.content
            
            # 检查响应是否包含坐标相关信息
            success = any(keyword in content.lower() for keyword in 
                         ["坐标", "coordinate", "点击", "click", "位置", "position"])
            
            self.log_test_result(
                "coordinate_integration",
                success,
                {
                    "message": f"坐标模型响应时间: {response_time:.2f}s",
                    "response": content,
                    "response_time": response_time,
                    "model_name": self.coordinate_model_name
                }
            )
            
            return success
            
        except Exception as e:
            self.log_test_result(
                "coordinate_integration",
                False,
                {
                    "message": f"坐标模型集成测试失败: {str(e)}",
                    "error": str(e)
                }
            )
            return False
    
    def test_performance(self) -> bool:
        """测试性能"""
        logger.info("⚡ 测试性能...")
        
        response_times = []
        success_count = 0
        total_tests = 5
        
        for i in range(total_tests):
            try:
                start_time = time.time()
                
                response = self.openai_client.chat.completions.create(
                    model=self.model_name,
                    messages=[{"role": "user", "content": f"这是性能测试 {i+1}，请简单回复。"}],
                    temperature=0,
                    max_tokens=50
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                response_times.append(response_time)
                
                if response.choices[0].message.content:
                    success_count += 1
                
                time.sleep(0.5)  # 短暂间隔
                
            except Exception as e:
                logger.warning(f"性能测试 {i+1} 失败: {e}")
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            
            # 性能标准：平均响应时间 < 10秒
            success = avg_response_time < 10.0 and success_count >= total_tests * 0.8
            
            self.log_test_result(
                "performance",
                success,
                {
                    "message": f"平均响应时间: {avg_response_time:.2f}s, 成功率: {success_count}/{total_tests}",
                    "avg_response_time": avg_response_time,
                    "min_response_time": min_response_time,
                    "max_response_time": max_response_time,
                    "success_rate": success_count / total_tests,
                    "total_tests": total_tests
                }
            )
            
            return success
        else:
            self.log_test_result(
                "performance",
                False,
                {
                    "message": "所有性能测试都失败了",
                    "error": "No successful requests"
                }
            )
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("🚀 开始UI-TARS模型测试...")
        self.start_time = time.time()
        
        # 运行各项测试
        tests = [
            ("基本连接测试", self.test_basic_connection),
            ("文本生成测试", self.test_text_generation),
            ("UI理解能力测试", self.test_ui_understanding),
            ("坐标模型集成测试", self.test_coordinate_model_integration),
            ("性能测试", self.test_performance)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"执行: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                if test_func():
                    passed_tests += 1
            except Exception as e:
                logger.error(f"{test_name} 执行异常: {e}")
        
        # 生成测试报告
        end_time = time.time()
        total_time = end_time - self.start_time
        
        summary = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": passed_tests / total_tests,
            "total_time": total_time,
            "model_config": {
                "model_name": self.model_name,
                "temperature": self.temperature,
                "base_url": self.config.llms["ui_tars"].external_args.get("base_url")
            },
            "detailed_results": self.test_results
        }
        
        self.print_summary(summary)
        self.save_report(summary)
        
        return summary
    
    def print_summary(self, summary: Dict[str, Any]):
        """打印测试摘要"""
        logger.info(f"\n{'='*60}")
        logger.info("🎯 UI-TARS模型测试报告")
        logger.info(f"{'='*60}")
        logger.info(f"模型名称: {summary['model_config']['model_name']}")
        logger.info(f"基础URL: {summary['model_config']['base_url']}")
        logger.info(f"温度设置: {summary['model_config']['temperature']}")
        logger.info(f"")
        logger.info(f"总测试数: {summary['total_tests']}")
        logger.info(f"通过测试: {summary['passed_tests']}")
        logger.info(f"失败测试: {summary['failed_tests']}")
        logger.info(f"成功率: {summary['success_rate']:.1%}")
        logger.info(f"总耗时: {summary['total_time']:.2f}秒")
        logger.info(f"")
        
        if summary['success_rate'] >= 0.8:
            logger.info("🎉 测试结果: 优秀")
        elif summary['success_rate'] >= 0.6:
            logger.info("✅ 测试结果: 良好")
        else:
            logger.info("⚠️ 测试结果: 需要改进")
        
        logger.info(f"{'='*60}")
    
    def save_report(self, summary: Dict[str, Any]):
        """保存测试报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"tests/ui_tars_test_report_{timestamp}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            logger.info(f"📄 测试报告已保存到: {report_file}")
        except Exception as e:
            logger.error(f"保存测试报告失败: {e}")


def main():
    """主函数"""
    try:
        tester = UITarsModelTester()
        summary = tester.run_all_tests()
        
        # 返回适当的退出码
        if summary['success_rate'] >= 0.8:
            sys.exit(0)  # 成功
        else:
            sys.exit(1)  # 失败
            
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
